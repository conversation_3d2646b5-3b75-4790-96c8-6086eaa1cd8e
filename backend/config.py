import os
from google.oauth2 import service_account
from dotenv import load_dotenv

# ===============================
# Load .env file
# ===============================
load_dotenv()  # <-- this actually loads your .env vars into os.environ

# ===============================
# Google Cloud Config
# ===============================
SERVICE_ACCOUNT_FILE = os.path.join(os.path.dirname(__file__), "service_account.json")
credentials = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE)

PROJECT_ID = credentials.project_id
DATASET_ID = "walmart"
USERS_TABLE = "users"

# ===============================
# API Keys (must come from env)
# ===============================
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
CRAWLBASE_API_KEY = os.getenv("CRAWLBASE_API_KEY")
SERPER_API_KEY = os.getenv("SERPER_API_KEY")

_missing = []
if not GEMINI_API_KEY:
    _missing.append("GEMINI_API_KEY")
if not CRAWLBASE_API_KEY:
    _missing.append("CRAWLBASE_API_KEY")
if not SERPER_API_KEY:
    _missing.append("SERPER_API_KEY")

if _missing:
    raise RuntimeError(f"Missing required env vars: {', '.join(_missing)}")

# ===============================
# Constants
# ===============================
DEFAULT_DOMAIN = "Vehicle"
SCHEMA_PATH = "routes/output.json"

HTTP_TIMEOUT = 30
CONCURRENT_LIMIT = 5
PLAIN_TEXT_SLICE = 1600
MAX_COMPETITORS = 3
