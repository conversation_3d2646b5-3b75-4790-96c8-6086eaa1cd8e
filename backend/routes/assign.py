import logging
from typing import Any, Dict, List   
from google.cloud import bigquery
from pydantic import BaseModel
from database import bq_client
from fastapi import APIRouter, HTTPException, Body, UploadFile, File
import pandas as pd
from io import BytesIO
import itertools

router = APIRouter()

logging.basicConfig(
    level=logging.INFO,  # 👈 make sure INFO is visible
    format="%(asctime)s - %(levelname)s - %(message)s"
)


# --- Save rows into BigQuery ---
TABLE = "ops-excellence.walmart.assign"

import itertools

TABLE = "ops-excellence.walmart.assign"

class SaveRequest(BaseModel):
    rows: List[Dict[str, Any]]

# global counter for auto-increment id
# (⚠️ works only within one process, not across restarts → BigQuery can also handle AUTO_INCREMENT using a surrogate key approach)
_id_counter = itertools.count(1)  # start at 1, can also query max(id)+1

def clean_value(value):
    """
    Normalize values before inserting into BigQuery.
    - Replace NaN/None with None
    - Convert numpy types to Python types
    """
    import pandas as pd
    import numpy as np

    if value is None:
        return None
    if isinstance(value, float) and pd.isna(value):
        return None
    if isinstance(value, (np.generic,)):  # numpy int/float/bool
        return value.item()
    return value


@router.post("/save")
async def save_assign(data: SaveRequest):
    try:
        rows_to_insert = []

        for r in data.rows:
            clean_rec = {k: clean_value(v) for k, v in r.items()}

            # ✅ Ensure unique auto-increment id
            clean_rec["id"] = next(_id_counter)

            # ✅ Ensure assign column
            if not clean_rec.get("assign"):
                clean_rec["assign"] = "Unassigned"

            rows_to_insert.append(clean_rec)

        if not rows_to_insert:
            raise HTTPException(status_code=400, detail="No rows to save")

        # Insert into BigQuery
        errors = bq_client.insert_rows_json(TABLE, rows_to_insert)
        if errors:
            logging.error(f"BigQuery insert error: {errors}")
            raise HTTPException(
                status_code=500, detail=f"BigQuery insert error: {errors}"
            )

        return {
            "message": "✅ Data saved successfully",
            "saved": len(rows_to_insert),
        }

    except Exception as e:
        logging.error(f"Error saving assign data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/")
def get_assign(limit: int = 100, offset: int = 0, search: str = "", username: str = ""):
    TABLE_PROJECT = "ops-excellence"
    DATASET = "walmart"
    TABLE = "assign"

    where_clauses = []
    params = [
        bigquery.ScalarQueryParameter("limit", "INT64", limit),
        bigquery.ScalarQueryParameter("offset", "INT64", offset),
    ]

    # --- Username filter ---
    if username:
        where_clauses.append("assign = @username")
        params.append(bigquery.ScalarQueryParameter("username", "STRING", username))

    # --- Search filter (multi-field, case-insensitive) ---
    if search:
        where_clauses.append("""
            (
                LOWER(product_name) LIKE @search OR
                LOWER(product_type) LIKE @search OR
                LOWER(category) LIKE @search OR
                CAST(item_id AS STRING) LIKE @search
            )
        """)
        params.append(bigquery.ScalarQueryParameter("search", "STRING", f"%{search.lower()}%"))

    where_clause = "WHERE " + " AND ".join(where_clauses) if where_clauses else ""

    query = f"""
        SELECT * 
        FROM `{TABLE_PROJECT}.{DATASET}.{TABLE}`
        {where_clause}
        ORDER BY date_yyyy_mm_dd DESC
        LIMIT @limit OFFSET @offset
    """
    job_config = bigquery.QueryJobConfig(query_parameters=params)

    try:
        # Fetch rows
        results = bq_client.query(query, job_config=job_config).result()
        rows = [dict(row) for row in results]

        # Fetch total count
        count_query = f"""
            SELECT COUNT(*) as total 
            FROM `{TABLE_PROJECT}.{DATASET}.{TABLE}`
            {where_clause}
        """
        count_job = bq_client.query(count_query, job_config=bigquery.QueryJobConfig(query_parameters=params))
        total_count = list(count_job.result())[0]["total"]

        return {"rows": rows, "count": total_count}

    except Exception as e:
        logging.error(f"Error fetching assign data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))




# @router.post("/upload")
# def upload_assign(payload: TaskPayload):
#     assign_table = "ops-excellence.walmart.assign"

#     rows_to_insert = [{
#         "submission_id": t.id,
#         "product_name": t.name,
#         "associate_walmart_id": t.assignedTo,
#         "item_status": t.status,
#     } for t in payload.tasks]

#     if not rows_to_insert:
#         raise HTTPException(status_code=400, detail="No tasks provided")

#     errors = bq_client.insert_rows_json(assign_table, rows_to_insert)

#     if errors:
#         logging.error(f"BigQuery insert error: {errors}")
#         raise HTTPException(status_code=500, detail=f"BigQuery insert error: {errors}")

#     return {"message": "✅ Tasks saved", "saved": len(rows_to_insert)}


# --- Update Status ---
class UpdateStatusRequest(BaseModel):
    id_str: str   # unique identifier for the row
    new_status: str
    username: str | None = None   # optional auditing field


@router.post("/update-status")
def update_status(payload: UpdateStatusRequest):
    id_str = payload.id_str
    new_status = payload.new_status
    table_ref = "ops-excellence.walmart.assign"

    logging.info(f"📡 Update request received - id_str={id_str}, new_status={new_status}")

    try:
        query = f"""
        UPDATE `{table_ref}`
        SET status = @status
        WHERE id_str = @id_str
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("status", "STRING", new_status),
                bigquery.ScalarQueryParameter("id_str", "STRING", id_str),
            ]
        )

        # Run update
        bq_client.query(query, job_config=job_config).result()

        logging.info(f"✅ Status updated successfully for {id_str}")
        return {"message": "✅ Status updated", "id_str": id_str, "new_status": new_status}

    except Exception as e:
        logging.error(f"❌ Error updating status for {id_str}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# --- Status Summary ---
@router.get("/status-summary")
def get_status_summary(username: str = ""):
    TABLE_PROJECT = "ops-excellence"
    DATASET = "walmart"
    TABLE = "assign"
    table_ref = f"{TABLE_PROJECT}.{DATASET}.{TABLE}"

    where_clause = "WHERE assign = @username" if username else ""
    params = []
    if username:
        params.append(bigquery.ScalarQueryParameter("username", "STRING", username))

    query = f"""
    SELECT 
        status,
        COUNT(*) AS count
    FROM `{table_ref}`
    {where_clause}
    GROUP BY status
    """

    try:
        logging.info(f"📡 Running status summary query for user={username}...")
        query_job = bq_client.query(query, job_config=bigquery.QueryJobConfig(query_parameters=params))
        results = query_job.result()

        summary = {"Pending": 0, "In Progress": 0, "Completed": 0}
        for row in results:
            status = row["status"] or "Pending"
            count = row["count"]

            if status not in summary:
                summary[status] = count
            else:
                summary[status] += count

        logging.info(f"✅ Final Summary for {username}: {summary}")
        return summary
    except Exception as e:
        logging.error(f"❌ Error fetching status summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/users-status-summary")
def get_users_status_summary():
    TABLE_PROJECT = "ops-excellence"
    DATASET = "walmart"
    TABLE = "assign"
    table_ref = f"{TABLE_PROJECT}.{DATASET}.{TABLE}"

    query = f"""
    SELECT assign AS username,
           status,
           COUNT(*) AS count
    FROM `{table_ref}`
    GROUP BY username, status
    ORDER BY username
    """

    try:
        results = bq_client.query(query).result()
        summary_dict = {}

        for row in results:
            user = row["username"] or "Unassigned"
            status = row["status"] or "Pending"
            count = row["count"]

            if user not in summary_dict:
                summary_dict[user] = {"Pending": 0, "In Progress": 0, "Completed": 0}

            summary_dict[user][status] = count

        # Convert to list
        summary_list = [
            {"username": u, **summary_dict[u]} for u in summary_dict
        ]

        return summary_list
    except Exception as e:
        logging.error(f"❌ Error fetching user status summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))

