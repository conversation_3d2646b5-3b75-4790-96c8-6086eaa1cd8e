import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, CheckCircle, Database, FileSpreadsheet } from "lucide-react";
import { processLlmValue,processDoublePipe } from "@/lib/utils";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

interface EnhancedFinalValue {
  Attribute?: string;
  finalValue: string;
  source: string;
  walmartValidation?: string;
  llmValidation?: string;
  walmartLatestValidation?: string; // ← ADDED
  walmartComment?: string;
  llmComment?: string;
  walmartLatestComments?: string;   // ← ADDED
  finalValidation?: string;
  finalComment?: string;
  finalVerdict?: string; // ✅ NEW
  rawData?: {
    walmart: string;
    llm: string;
    brand: string;
    competitors: Record<string, string>;
  };
}

interface FeedbackFormProps {
  responseText: string;
  id_str: string;
  finalValues: EnhancedFinalValue[];
  shortSummary: string;
  detailedSummary: string;
}

export default function FeedbackForm({ 
  responseText, 
  id_str, 
  finalValues,
  shortSummary,
  detailedSummary
}: FeedbackFormProps) {
  const [feedback, setFeedback] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");

  const [saveDetails, setSaveDetails] = useState<{
    columns: number;
    sheetName: string;
    spreadsheetUrl?: string;
    rowsAdded?: number;
    taskViewUrl?: string;
    bucketPath?: string;
  } | null>(null);
  const navigate = useNavigate();

  // ✅ COMPLETELY UPDATED transformToWideFormat to match CSV export exactly
  const transformToWideFormat = () => {
    try {
      const parsedResponse = JSON.parse(responseText);
      const wideData: Record<string, string> = {};

      // Helper function to clean NaN values (same as in ResponseArea)
      const cleanNaNValue = (value: any): string => {
        if (value === null || value === undefined || value === "" ||
          String(value).toLowerCase() === "nan" ||
          String(value).toLowerCase() === "null") {
          return "-";
        }
        return String(value);
      };

      console.log("📄 Starting wide format transformation for Google Sheets");

let submittedBy = "";
      try {
        const storedUser = localStorage.getItem("user");
        if (storedUser) {
          const u = JSON.parse(storedUser);
          submittedBy = u?.username || u?.name || u?.email || "";
        }
      } catch (e) {
        // ignore
      }
      if (submittedBy) wideData["user"] = submittedBy;      

      // ✅ Basic info columns (EXACT MATCH to CSV export)
      wideData['Timestamp'] = new Date().toISOString();
      wideData['Submission ID'] = id_str || '';
      wideData["Walmart URL"] = cleanNaNValue(parsedResponse.walmart?.url);
      wideData["Walmart_Latest URL"] = cleanNaNValue(parsedResponse.walmart_llm?.url);
      wideData["Product Type"] = cleanNaNValue(parsedResponse.product_type);
      wideData["Category"] = cleanNaNValue(parsedResponse.category);

      // Extract Product ID from URL
      const walmartUrl = cleanNaNValue(parsedResponse.walmart?.url);
      if (walmartUrl && walmartUrl !== "-" && walmartUrl.includes("walmart.com/ip/")) {
        const productId = walmartUrl.split("/ip/")[1]?.split("/")[0] || "";
        wideData["Product ID"] = cleanNaNValue(parsedResponse.walmart.product_id);
        wideData["Item ID"] = cleanNaNValue(productId);
      } else {
        wideData["Product ID"] = "-";
        wideData["Item ID"] = "-";
      }

      wideData["Product Name"] = "-"; // Will be filled if available
      wideData["Final LLM Link"] = "-"; // Will be filled if available

      // ✅ NEW: Get all competitor columns dynamically (MATCHES CSV)
      const allCompetitors = new Set<string>();
      
      // Extract competitors from response data
      if (parsedResponse.competitors) {
        parsedResponse.competitors.forEach((comp: any, index: number) => {
          if (comp && comp.url) {
            try {
              const hostname = new URL(comp.url).hostname || "";
              const competitorName = hostname.replace(/^www\./, "").split(".")[0] || `competitor${index + 1}`;
              allCompetitors.add(competitorName);
            } catch (e) {
              allCompetitors.add(`competitor${index + 1}`);
            }
          }
        });
      }

      // Also check finalValues for any competitor data
      finalValues.forEach(fv => {
        if (fv.rawData?.competitors) {
          Object.keys(fv.rawData.competitors).forEach(comp => {
            allCompetitors.add(comp);
          });
        }
      });

      console.log(`Found ${allCompetitors.size} competitors:`, Array.from(allCompetitors));

      // ✅ NEW: Add competitor source URLs to basic info (MATCHES CSV)
      allCompetitors.forEach(competitor => {
        const competitorUrl = parsedResponse.competitors?.find((c: any) => {
          if (!c?.url) return false;
          try {
            const hostname = new URL(c.url).hostname || "";
            const name = hostname.replace(/^www\./, "").split(".")[0];
            return name === competitor;
          } catch {
            return false;
          }
        })?.url || "";
        wideData[`${competitor} Source URL`] = cleanNaNValue(competitorUrl);
      });

      // Get all unique attributes from finalValues
      const allAttributes = new Set<string>();
      finalValues.forEach(fv => {
        if (fv.Attribute) {
          allAttributes.add(fv.Attribute);
        }
      });

      console.log(`Processing ${allAttributes.size} attributes for Google Sheets wide format`);

      // ✅ Process each attribute with ALL COLUMNS from CSV export
      Array.from(allAttributes).forEach(attribute => {
        const fvIndex = finalValues.findIndex(fv => fv.Attribute === attribute);
        const fv = fvIndex !== -1 ? finalValues[fvIndex] : null;

        // ✅ Original data columns (MATCHES CSV)
        wideData[`${attribute}_Initial prefilled values`] = processDoublePipe(cleanNaNValue(parsedResponse.walmart?.attributes?.[attribute]), attribute);
        wideData[`${attribute}_Walmart_Latest values`] = processDoublePipe(cleanNaNValue(parsedResponse.walmart_llm?.attributes?.[attribute]), attribute);
        
        // ✅ LLM data (MATCHES CSV)
        const llmData = parsedResponse.llm_suggested?.[attribute];
        wideData[`${attribute} LLM Suggested value`] = processLlmValue(llmData?.value || llmData, attribute);
        wideData[`${attribute} LLM_suggested_url`] = cleanNaNValue(llmData?.source_url || "-");
        
        // ✅ Current values (MATCHES CSV)
        wideData[`${attribute}_Walmart_value`] = processDoublePipe(cleanNaNValue(fv?.rawData?.walmart || parsedResponse.walmart?.attributes?.[attribute]), attribute); // cleanNaNValue(fv?.rawData?.walmart || parsedResponse.walmart?.attributes?.[attribute]);
        wideData[`${attribute}_Brand_value`] = cleanNaNValue(fv?.rawData?.brand || parsedResponse.brand?.attributes?.[attribute]);
        wideData[`${attribute}_WalmartLatest_value`] = processDoublePipe(cleanNaNValue(parsedResponse.walmart_llm?.attributes?.[attribute]), attribute);        wideData[`${attribute}_LLM_value`] = cleanNaNValue(processLlmValue(fv?.rawData?.llm || llmData?.value || llmData, attribute));
        
        // ✅ NEW: Add ALL competitor values (MATCHES CSV)
        allCompetitors.forEach(competitor => {
          let competitorValue = "-";
          
          // Try to get from finalValues rawData
          if (fv?.rawData?.competitors?.[competitor]) {
            competitorValue = fv.rawData.competitors[competitor];
          } else {
            // Try to get from parsedResponse competitors
            const compData = parsedResponse.competitors?.find((c: any) => {
              if (!c?.url) return false;
              try {
                const hostname = new URL(c.url).hostname || "";
                const name = hostname.replace(/^www\./, "").split(".")[0];
                return name === competitor;
              } catch {
                return false;
              }
            });
            if (compData?.attributes?.[attribute]) {
              competitorValue = compData.attributes[attribute];
            }
          }
          
          wideData[`${attribute}_${competitor}_value`] = cleanNaNValue(competitorValue);
        });
        
        // ✅ Validation data (MATCHES CSV)
        wideData[`${attribute}_Walmart_Validated`] = cleanNaNValue(fv?.walmartValidation || "");
        wideData[`${attribute}_LLM_Validate`] = cleanNaNValue(fv?.llmValidation || "");
        wideData[`${attribute}_LLM_Validation`] = cleanNaNValue(fv?.llmValidation || "");
       //wideData[`${attribute}_Final_Validated`] = allValidations.join("| ") || "-"; 
        wideData[`${attribute}_Final_Validation`] = cleanNaNValue(fv?.finalValidation || "");
        // console.log(fv?.finalValidation);
        // console.log( wideData[`${attribute}_Final_Validation`]);
        
        // ✅ Comments data (MATCHES CSV)
        wideData[`${attribute}_Walmart_Comment`] = cleanNaNValue(fv?.walmartComment || "");
        wideData[`${attribute}_WalmartLatest_Comment`] = cleanNaNValue(fv?.walmartLatestComments || ""); // ← ADDED
        wideData[`${attribute}_LLM_Comment`] = cleanNaNValue(fv?.llmComment || "");
// ✅ FIXED: Include WalmartLatest in final comment
        const allComments = [
          fv?.walmartComment,
          fv?.walmartLatestComments,
          fv?.llmComment
        ].filter(c => c && c.trim() && c !== "-").map(c => c!.replace(/-/g, "").replace(/\b\w/g, l => l.toUpperCase()));

        wideData[`${attribute}_Final_Comment`] = allComments.join(" | ") || "-";        
        // ✅ NEW: Final Verdict (MATCHES CSV)
        wideData[`${attribute}_Final_Verdict`] = cleanNaNValue(fv?.finalVerdict || "");

        //wideData[`${attribute}_Final_Validation`] = cleanNaNValue(fv?.finalValidation || "");

        // ✅ Final combined values (MATCHES CSV)
        wideData[`${attribute}_Final_Choice`] = cleanNaNValue(fv?.finalValue || "");
        wideData[`${attribute}_Final_Source`] = cleanNaNValue(fv?.source || "");
        wideData[`${attribute}_Final_Verdict_Combined`] = cleanNaNValue(fv?.finalVerdict || "");
        
        // ✅ NEW: Selection status (MATCHES CSV)
        const selectedSources = [];
        // Note: This would need to be passed from the parent component
        // For now, we'll determine from finalValue content
        if (fv?.finalValue && fv.finalValue !== "-") {
          if (fv.source?.includes("Walmart")) selectedSources.push("Walmart");
          if (fv.source?.includes("LLM")) selectedSources.push("LLM");
          if (fv.source?.includes("Brand")) selectedSources.push("Brand");
          allCompetitors.forEach(comp => {
            if (fv.source?.toLowerCase().includes(comp.toLowerCase())) {
              selectedSources.push(comp);
            }
          });
        }
        wideData[`${attribute}_Selected_Sources`] = selectedSources.join(", ") || "-";
      });
      console.log(wideData);
      
      // ✅ Summary statistics (MATCHES CSV)
      const validationCounts = {
        walmartYes: 0,
        walmartNo: 0,
        walmartLatestYes: 0, // ← ADDED
        walmartLatestNo: 0,  // ← ADDED
        llmYes: 0,
        llmNo: 0,
        finalVerdicts: 0
      };

      finalValues.forEach(fv => {
        if (fv.walmartValidation?.toLowerCase() === 'yes') validationCounts.walmartYes++;
        if (fv.walmartValidation?.toLowerCase() === 'no') validationCounts.walmartNo++;
        if (fv.walmartLatestValidation?.toLowerCase() === 'yes') validationCounts.walmartLatestYes++; // ← ADDED
        if (fv.walmartLatestValidation?.toLowerCase() === 'no') validationCounts.walmartLatestNo++;   // ← ADDED
        if (fv.llmValidation?.toLowerCase() === 'yes') validationCounts.llmYes++;
        if (fv.llmValidation?.toLowerCase() === 'no') validationCounts.llmNo++;
        if (fv.finalVerdict && fv.finalVerdict.trim()) validationCounts.finalVerdicts++;
      });

      wideData['Total_Attributes'] = String(allAttributes.size);
      wideData['Total_Selections'] = "0"; // Would need to be passed from parent
      wideData['Total_Competitors'] = String(allCompetitors.size);
      wideData['Competitor_Names'] = Array.from(allCompetitors).join(", ") || "-";
      wideData['Walmart_Yes_Count'] = String(validationCounts.walmartYes);
      wideData['Walmart_No_Count'] = String(validationCounts.walmartNo);
      wideData['WalmartLatest_Yes_Count'] = String(validationCounts.walmartLatestYes); // ← ADDED
      wideData['WalmartLatest_No_Count'] = String(validationCounts.walmartLatestNo);   // ← ADDED
      wideData['LLM_Yes_Count'] = String(validationCounts.llmYes);
      wideData['LLM_No_Count'] = String(validationCounts.llmNo);
      wideData['Final_Verdicts_Count'] = String(validationCounts.finalVerdicts);

      console.log(`✅ Wide format data prepared with ${Object.keys(wideData).length} fields for Google Sheets`);
      console.log(`✅ Competitors included: ${Array.from(allCompetitors).join(', ')}`);
      console.log(`✅ WalmartLatest validation counts: Yes=${validationCounts.walmartLatestYes}, No=${validationCounts.walmartLatestNo}`); // ← ADDED

      return wideData;
    } catch (error) {
      console.error("❌ Error transforming to wide format for Google Sheets:", error);
      return {};
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setErrorMsg("");
    setSaveDetails(null);

    const wideFormatData = transformToWideFormat();
    
    // ✅ Enhanced payload with Google Sheets specific data
    const feedbackPayload = {
      id_str,
      feedback,
      finalValues,
      submitted_by: wideFormatData["Submitted By"] || "",
      shortSummary,
      detailedSummary,
      rawJson: responseText,
      wideFormatData,
      saveToGoogleSheets: true,
      sheetMetadata: {
        productId: wideFormatData[" GTIN"] || id_str,
        timestamp: new Date().toISOString(),
        totalColumns: Object.keys(wideFormatData).length,
        totalAttributes: finalValues.length,
        totalCompetitors: parseInt(wideFormatData['Total_Competitors'] || '0'),
        competitorNames: wideFormatData['Competitor_Names']
      }
    };

    try {
      console.log("📤 Sending enhanced payload to backend for Google Sheets save");
      console.log(`📊 Payload contains ${Object.keys(wideFormatData).length} columns`);
      
      const fbRes = await fetch(`${API_BASE_URL}/query/feedback`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(feedbackPayload),
      });

      if (!fbRes.ok) {
        const errorData = await fbRes.text();
        throw new Error(`Feedback failed: ${errorData}`);
      }

      const feedbackResult = await fbRes.json();
      console.log("📊 Google Sheets save result:", feedbackResult);

      // ✅ Enhanced save details with Google Sheets specific info
      setSaveDetails({
        columns: feedbackResult.wide_format_columns || Object.keys(wideFormatData).length,
        sheetName: feedbackResult.saved_to_sheet || "Product Analysis Data",
        spreadsheetUrl: feedbackResult.spreadsheet_url,
        rowsAdded: feedbackResult.rows_added || 1
      });
      setSubmitted(true);

      // update status
      await fetch(`${API_BASE_URL}/assign/update-status`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id_str, new_status: "Completed" }),
      });

      setTimeout(() => navigate("/user"), 3000);
    } catch (err: any) {
      console.error("❌ Error saving to Google Sheets:", err);
      setErrorMsg(err.message || "Failed to submit feedback and save to Google Sheets. Try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mt-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 p-5 space-y-4 shadow-sm">
      <h3 className="font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
        <FileSpreadsheet className="h-5 w-5 text-green-600" />
        Submit Feedback & Save to Google Sheets
      </h3>

      {/* Textarea */}
      <Textarea
        placeholder="Share your feedback about the data quality, accuracy, and any issues you encountered..."
        value={feedback}
        onChange={(e) => setFeedback(e.target.value)}
        disabled={submitted || loading}
        className="resize-none"
        rows={4}
      />

      {/* Submit Button */}
      {!submitted && (
        <Button
          onClick={handleSubmit}
          disabled={!feedback.trim() || loading}
          className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Saving to Google Sheets...
            </>
          ) : (
            <>
              <FileSpreadsheet className="h-4 w-4" />
              Submit & Save to Sheets
            </>
          )}
        </Button>
      )}

      {/* Animated Messages */}
      <AnimatePresence mode="wait">
        {submitted && saveDetails && (
          <motion.div
            key="success"
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -8 }}
            className="space-y-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800"
          >
            <div className="flex items-center gap-2 text-green-700 dark:text-green-400 font-medium">
              <CheckCircle className="h-5 w-5" />
              ✅ Successfully saved to Google Sheets!
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                <Database className="h-4 w-4" />
                📊 {saveDetails.columns} columns saved to "{saveDetails.sheetName}"
              </div>
              
              {saveDetails.rowsAdded && (
                <div className="flex items-center gap-2 text-purple-600 dark:text-purple-400">
                  <FileSpreadsheet className="h-4 w-4" />
                  📝 {saveDetails.rowsAdded} row(s) added to spreadsheet
                </div>
              )}
              
              {saveDetails.spreadsheetUrl && (
                <div className="mt-2">
                  <a
                    href={saveDetails.spreadsheetUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                  >
                    🔗 View Spreadsheet
                  </a>
                </div>
              )}
            </div>
            
            <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
              Redirecting to dashboard in 3 seconds...
            </div>
          </motion.div>
        )}

        {errorMsg && (
          <motion.p
            key="error"
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -8 }}
            className="text-red-600 dark:text-red-400 font-medium p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800"
          >
            ❌ {errorMsg}
          </motion.p>
        )}
      </AnimatePresence>
    </div>
  );
}

