export function cn(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}


// Fields that need comma-to-pipe replacement for LLM values
export const fieldsRequiringCommaReplacement: string[] = [
  "Automotive Replacement Belt Type",
  "Automotive Specialty Part Type",
  "Automotive Specialty Tool Type",
  "Automotive Valve Stem & Cap Type",
  "Brake System Replacement Part & Hardware Type",
  "Color Category",
  "Compatible Cars",
  "Features",
  "Fuel Type",
  "Hardware Nut Type",
  "Items Included",
  "Material",
  "Motor Starter Type",
  "Power Type",
  "Product Long Description",
  "Product Secondary Image URL",
  "Recommended Use",
  "Third Party Accreditation Symbol on Product Package Code",
  "Vehicle Mount Location",
  "Vehicle Type",
  "Volts"
];

/**
 * Cleans a value and applies comma-to-pipe replacement if the attribute requires it.
 */
export const processLlmValue = (value: any, attributeName: string): string => {
  if (
    value === null ||
    value === undefined ||
    value === "" ||
    String(value).toLowerCase() === "nan" ||
    String(value).toLowerCase() === "null"
  ) {
    return "-";
  }

  let cleanedValue: string;

  if (typeof value === 'object' && value !== null) {
    // Handle nested objects like {value: "120", unit: "g"}
    if (value.value !== undefined && value.unit !== undefined) {
      cleanedValue = `${value.value} ${value.unit}`;
    } else {
      // Fallback for other objects
      cleanedValue = JSON.stringify(value);
    }
  } else {
    cleanedValue = String(value);
  }

  if (fieldsRequiringCommaReplacement.includes(attributeName)) {
    let pipedValue = cleanedValue
      .trim().replace(/,/g, "|")
      .replace(/\|{2,}/g, "|");
    return pipedValue;
  }

  return cleanedValue;
};

export const processDoublePipe = (value: any, attributeName: string): string => {
  if (
    value === null ||
    value === undefined ||
    value === "" ||
    String(value).toLowerCase() === "nan" ||
    String(value).toLowerCase() === "null"
  ) {
    return "-";
  }

  const cleanedValue = String(value);

  let pipedValue = cleanedValue
    .replace(/\|{2,}/g, "|");
  return pipedValue;
};

/**
 * Processes values for TaskView table - converts commas and double pipes to single pipes
 * This function handles both comma-to-pipe and double-pipe-to-single-pipe conversion
 */
export const processTaskViewValue = (value: any, attributeName: string): string => {
  if (
    value === null ||
    value === undefined ||
    value === "" ||
    String(value).toLowerCase() === "nan" ||
    String(value).toLowerCase() === "null"
  ) {
    return "-";
  }

  let cleanedValue: string;

  if (typeof value === 'object' && value !== null) {
    // Handle nested objects like {value: "120", unit: "g"}
    if (value.value !== undefined && value.unit !== undefined) {
      cleanedValue = `${value.value} ${value.unit}`;
    } else {
      // Fallback for other objects
      cleanedValue = JSON.stringify(value);
    }
  } else {
    cleanedValue = String(value);
  }

  // Convert commas to pipes and then clean up any double pipes
  let pipedValue = cleanedValue
    .trim()
    .replace(/, /g, "|")           // Replace commas with pipes
    .replace(/\|{2,}/g, "|")      // Replace multiple pipes with single pipe
    .replace(/^\|+|\|+$/g, "");   // Remove leading/trailing pipes

  return pipedValue || "-";
};
