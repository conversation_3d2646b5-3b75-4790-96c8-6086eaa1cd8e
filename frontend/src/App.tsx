import { Routes, Route, Navigate, useLocation, useNavigate } from "react-router-dom";
import React, { Suspense, useEffect, useState } from "react";
import { useAppStore } from "./store/app";
import { getSession } from "./lib/session";

// Lazy-loaded pages to reduce initial bundle size
const LoginPage = React.lazy(() => import("./pages/LoginPage"));
const SignUpPage = React.lazy(() => import("./pages/SignUpPage"));
const AdminPage = React.lazy(() => import("./pages/AdminPage"));
const UserPage = React.lazy(() => import("./pages/UserPage"));
const ResponsePage = React.lazy(() => import("./pages/ResponsePage"));
const Navbar = React.lazy(() => import("./components/Navbar"));

function RequireAuth({
  children,
  allowedRoles,
}: {
  children: React.ReactNode;
  allowedRoles: string[];
}) {
  const role = useAppStore((s) => s.role);

  if (!role) {
    return <Navigate to="/" replace />;
  }

  if (!allowedRoles.includes(role)) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
}

export default function App() {
  const location = useLocation();
  const navigate = useNavigate();
  const setRole = useAppStore((s) => s.setRole);
  const logout = useAppStore((s) => s.logout);

  const [checkingSession, setCheckingSession] = useState(true);

  useEffect(() => {
    const session = getSession();

    if (!session) {
      logout();
      if (
        location.pathname !== "/" &&
        location.pathname !== "/login" &&
        location.pathname !== "/signup"
      ) {
        navigate("/", { replace: true });
      }
      setCheckingSession(false);
    } else {
      setRole(session.role, session.userId);

      const publicPaths = ["/", "/login", "/signup"];
      if (publicPaths.includes(location.pathname)) {
        if (session.role === "admin") {
          navigate("/admin", { replace: true });
        } else if (session.role === "user") {
          navigate("/user", { replace: true });
        }
      }
      setCheckingSession(false);
    }
  }, [location.pathname]);

  // Comprehensive swipe navigation prevention
  useEffect(() => {
    // Method 1: CSS overscroll-behavior (most effective)
    const html = document.documentElement;
    const body = document.body;
    
    // Store original values for cleanup
    const originalHtmlX = html.style.overscrollBehaviorX;
    const originalHtmlY = html.style.overscrollBehaviorY;
    const originalBodyX = body.style.overscrollBehaviorX;
    const originalBodyY = body.style.overscrollBehaviorY;

    // Apply overscroll prevention
    html.style.overscrollBehaviorX = 'none';
    html.style.overscrollBehaviorY = 'none';
    body.style.overscrollBehaviorX = 'none';
    body.style.overscrollBehaviorY = 'none';

    // Method 2: History manipulation backup
    const pushNeutralState = () => {
      try {
        window.history.pushState({ preventBack: true }, "", window.location.href);
      } catch (e) {
        // ignore
      }
    };

    const onPopState = (e: PopStateEvent) => {
      if (e.state && (e.state as any).preventBack) {
        pushNeutralState();
      }
    };

    // Method 3: Touch event prevention (backup for older browsers)
    const preventSwipeNavigation = (e: TouchEvent) => {
      // Only prevent if it's a potential navigation gesture (horizontal swipe from edge)
      if (e.touches.length === 2) {
        const touch = e.touches[0];
        if (touch.clientX <= 10 || touch.clientX >= window.innerWidth - 10) {
          e.preventDefault();
        }
      }
    };

    // Apply all methods
    pushNeutralState();
    window.addEventListener('popstate', onPopState);
    document.addEventListener('touchstart', preventSwipeNavigation, { passive: false });

    return () => {
      // Restore original CSS values
      html.style.overscrollBehaviorX = originalHtmlX;
      html.style.overscrollBehaviorY = originalHtmlY;
      body.style.overscrollBehaviorX = originalBodyX;
      body.style.overscrollBehaviorY = originalBodyY;
      
      // Remove event listeners
      window.removeEventListener('popstate', onPopState);
      document.removeEventListener('touchstart', preventSwipeNavigation);
    };
  }, []);

  if (checkingSession) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p className="text-gray-500">Loading...</p>
      </div>
    );
  }

  return (
    <Suspense fallback={<div className="flex justify-center items-center h-screen">Loading...</div>}>
      <Routes>
        <Route path="/" element={<LoginPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/signup" element={<SignUpPage />} />

        <Route
          path="/admin"
          element={
            <RequireAuth allowedRoles={["admin"]}>
              <Navbar />
              <AdminPage />
            </RequireAuth>
          }
        />
        <Route
          path="/user"
          element={
            <RequireAuth allowedRoles={["user"]}>
              <Navbar />
              <UserPage />
            </RequireAuth>
          }
        />
        <Route
          path="/response"
          element={
            <RequireAuth allowedRoles={["admin", "user"]}>
              <Navbar />
              <ResponsePage />
            </RequireAuth>
          }
        />

        {/* Catch-all redirect */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
}
